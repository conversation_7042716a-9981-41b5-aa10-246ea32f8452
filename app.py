import streamlit as st
import cv2
import threading
import time
from queue import Queue
import torch
from ultralytics import YOLO
import os
from functools import lru_cache

# Konfigurasi halaman Streamlit
st.set_page_config(
    page_title="SIBI Sign Language Detection",
    page_icon="🤟",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Kelas untuk mengelola kamera dengan optimasi
class OptimizedCamera:
    def __init__(self):
        self.cap = None
        self.frame_queue = Queue(maxsize=2)  # Buffer kecil untuk mengurangi lag
        self.running = False
        self.thread = None
        self.fps_target = 30
        self.frame_skip = 1  # Skip frame untuk optimasi
        self.frame_count = 0
        
    def start_camera(self, camera_index=0):
        """Memulai kamera dengan optimasi GPU"""
        try:
            self.cap = cv2.VideoCapture(camera_index)
            if not self.cap.isOpened():
                return False
                
            # Optimasi pengaturan kamera
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
            self.cap.set(cv2.CAP_PROP_FPS, self.fps_target)
            self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)  # Minimal buffer
            
            # Coba gunakan GPU backend jika tersedia
            try:
                self.cap.set(cv2.CAP_PROP_BACKEND, cv2.CAP_DSHOW)
            except:
                pass
                
            self.running = True
            self.thread = threading.Thread(target=self._capture_frames, daemon=True)
            self.thread.start()
            return True
        except Exception as e:
            st.error(f"Error starting camera: {e}")
            return False
    
    def _capture_frames(self):
        """Thread untuk capture frame secara kontinyu"""
        while self.running and self.cap is not None:
            ret, frame = self.cap.read()
            if ret:
                self.frame_count += 1
                # Skip frame untuk optimasi
                if self.frame_count % self.frame_skip == 0:
                    # Bersihkan queue jika penuh
                    if self.frame_queue.full():
                        try:
                            self.frame_queue.get_nowait()
                        except:
                            pass
                    
                    try:
                        self.frame_queue.put_nowait(frame)
                    except:
                        pass
            
            # Control FPS
            time.sleep(1.0 / self.fps_target)
    
    def get_frame(self):
        """Mendapatkan frame terbaru"""
        try:
            return self.frame_queue.get_nowait()
        except:
            return None
    
    def stop_camera(self):
        """Menghentikan kamera"""
        self.running = False
        if self.thread:
            self.thread.join(timeout=1.0)
        if self.cap:
            self.cap.release()

        # Bersihkan queue
        while not self.frame_queue.empty():
            try:
                self.frame_queue.get_nowait()
            except:
                break

# Kelas untuk mengelola sequence kata
class WordSequenceManager:
    def __init__(self, max_sequence_length=10, word_timeout=3.0):
        self.sequence = []
        self.last_detection_time = {}
        self.max_sequence_length = max_sequence_length
        self.word_timeout = word_timeout

    def add_detection(self, word, confidence):
        """Menambahkan deteksi kata ke sequence"""
        current_time = time.time()

        # Hanya tambahkan jika confidence cukup tinggi
        if confidence > 0.7:
            # Cek apakah kata ini baru saja terdeteksi
            if word not in self.last_detection_time or \
               current_time - self.last_detection_time[word] > self.word_timeout:

                self.sequence.append({
                    'word': word,
                    'confidence': confidence,
                    'timestamp': current_time
                })

                self.last_detection_time[word] = current_time

                # Batasi panjang sequence
                if len(self.sequence) > self.max_sequence_length:
                    self.sequence.pop(0)

    def get_sequence_text(self):
        """Mendapatkan text dari sequence kata"""
        if not self.sequence:
            return ""

        words = [item['word'] for item in self.sequence]
        return " ".join(words)

    def clear_sequence(self):
        """Membersihkan sequence"""
        self.sequence = []
        self.last_detection_time = {}

# Inisialisasi session state
if 'camera' not in st.session_state:
    st.session_state.camera = OptimizedCamera()
if 'camera_running' not in st.session_state:
    st.session_state.camera_running = False
if 'model_loaded' not in st.session_state:
    st.session_state.model_loaded = False
if 'model' not in st.session_state:
    st.session_state.model = None
if 'word_sequence' not in st.session_state:
    st.session_state.word_sequence = WordSequenceManager()
if 'detection_enabled' not in st.session_state:
    st.session_state.detection_enabled = False

# Fungsi untuk memuat model
@st.cache_resource
def load_model():
    """Memuat model YOLOv11 dengan caching"""
    try:
        model_path = "models/sibiv3.pt"
        if not os.path.exists(model_path):
            st.error(f"Model tidak ditemukan di: {model_path}")
            return None

        # Load model dengan GPU jika tersedia
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        model = YOLO(model_path)
        model.to(device)

        st.success(f"Model berhasil dimuat pada device: {device}")
        return model
    except Exception as e:
        st.error(f"Error loading model: {e}")
        return None

# Cache untuk class names
@lru_cache(maxsize=1)
def get_class_names():
    """Get cached class names"""
    return ["mau", "saya", "mana", "makan", "kamu", "jalan", "hotel", "ke", "di"]

# Fungsi untuk deteksi bahasa isyarat dengan optimasi
def detect_sign_language(frame, model, confidence_threshold=0.5):
    """Deteksi bahasa isyarat pada frame dengan optimasi"""
    try:
        # Resize frame untuk inferensi lebih cepat jika perlu
        height, width = frame.shape[:2]
        if width > 640:
            scale = 640 / width
            new_width = 640
            new_height = int(height * scale)
            inference_frame = cv2.resize(frame, (new_width, new_height))
            scale_back = width / 640
        else:
            inference_frame = frame
            scale_back = 1.0

        # Jalankan inferensi dengan optimasi
        with torch.no_grad():  # Disable gradient computation
            results = model(inference_frame, conf=confidence_threshold, verbose=False, device='cuda' if torch.cuda.is_available() else 'cpu')

        detections = []
        annotated_frame = frame.copy()
        class_names = get_class_names()

        for result in results:
            boxes = result.boxes
            if boxes is not None:
                for box in boxes:
                    # Ekstrak informasi deteksi
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = float(box.conf[0].cpu().numpy())
                    class_id = int(box.cls[0].cpu().numpy())

                    # Scale back coordinates jika frame di-resize
                    if scale_back != 1.0:
                        x1, y1, x2, y2 = x1 * scale_back, y1 * scale_back, x2 * scale_back, y2 * scale_back

                    # Mapping class ID ke nama kata
                    if class_id < len(class_names):
                        word = class_names[class_id]

                        detections.append({
                            'word': word,
                            'confidence': confidence,
                            'bbox': [int(x1), int(y1), int(x2), int(y2)]
                        })

                        # Gambar bounding box dan label
                        cv2.rectangle(annotated_frame, (int(x1), int(y1)), (int(x2), int(y2)), (0, 255, 0), 2)

                        # Label dengan confidence
                        label = f"{word}: {confidence:.2f}"
                        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]

                        # Background untuk text
                        cv2.rectangle(annotated_frame,
                                    (int(x1), int(y1) - label_size[1] - 10),
                                    (int(x1) + label_size[0], int(y1)),
                                    (0, 255, 0), -1)

                        # Text
                        cv2.putText(annotated_frame, label,
                                  (int(x1), int(y1) - 5),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 2)

        # Cleanup untuk memory management
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

        return annotated_frame, detections

    except Exception as e:
        st.error(f"Error dalam deteksi: {e}")
        return frame, []



# UI Streamlit
def main():
    st.title("🤟 SIBI Sign Language Detection")
    st.markdown("### Aplikasi Deteksi Bahasa Isyarat Indonesia Real-time")
    
    # Sidebar untuk kontrol
    with st.sidebar:
        st.header("⚙️ Pengaturan")
        
        # Info GPU
        if torch.cuda.is_available():
            st.success(f"✅ GPU Tersedia: {torch.cuda.get_device_name()}")
        else:
            st.warning("⚠️ Menggunakan CPU")
        
        # Kontrol kamera
        st.subheader("📹 Kontrol Kamera")
        
        camera_index = st.selectbox("Pilih Kamera", [0, 1, 2], index=0)
        
        col1, col2 = st.columns(2)
        with col1:
            if st.button("▶️ Start", use_container_width=True):
                if not st.session_state.camera_running:
                    if st.session_state.camera.start_camera(camera_index):
                        st.session_state.camera_running = True
                        st.rerun()
                    else:
                        st.error("Gagal memulai kamera")
        
        with col2:
            if st.button("⏹️ Stop", use_container_width=True):
                if st.session_state.camera_running:
                    st.session_state.camera.stop_camera()
                    st.session_state.camera_running = False
                    st.rerun()
        
        # Load model
        st.subheader("🤖 Model")
        if st.button("📥 Load Model", use_container_width=True):
            with st.spinner("Memuat model..."):
                st.session_state.model = load_model()
                st.session_state.model_loaded = st.session_state.model is not None
                st.rerun()

        if st.session_state.model_loaded:
            st.success("✅ Model siap digunakan")

            # Kontrol deteksi
            st.subheader("🔍 Deteksi")
            st.session_state.detection_enabled = st.checkbox(
                "Aktifkan Deteksi Real-time",
                value=st.session_state.detection_enabled
            )

            # Pengaturan confidence threshold
            if 'confidence_threshold' not in st.session_state:
                st.session_state.confidence_threshold = 0.5

            st.session_state.confidence_threshold = st.slider(
                "Confidence Threshold",
                min_value=0.1,
                max_value=1.0,
                value=st.session_state.confidence_threshold,
                step=0.1
            )

            # Tombol clear sequence
            if st.button("🗑️ Clear Sequence", use_container_width=True):
                st.session_state.word_sequence.clear_sequence()
                st.rerun()
        
        # Info kata yang dapat dideteksi
        st.subheader("📝 Kata yang Dapat Dideteksi")
        words = ["mau", "saya", "mana", "makan", "kamu", "jalan", "hotel", "ke", "di"]
        for word in words:
            st.write(f"• {word}")
    
    # Area utama untuk video
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("📺 Live Camera Feed")
        video_placeholder = st.empty()
        
        if st.session_state.camera_running:
            # Placeholder untuk FPS counter
            fps_placeholder = st.empty()

            # Loop untuk menampilkan frame
            frame_count = 0
            start_time = time.time()

            while st.session_state.camera_running:
                frame = st.session_state.camera.get_frame()

                if frame is not None:
                    # Flip frame horizontal untuk efek mirror
                    frame = cv2.flip(frame, 1)

                    # Jalankan deteksi jika diaktifkan
                    if (st.session_state.detection_enabled and
                        st.session_state.model_loaded and
                        st.session_state.model is not None):

                        # Deteksi bahasa isyarat
                        annotated_frame, detections = detect_sign_language(
                            frame,
                            st.session_state.model,
                            confidence_threshold=st.session_state.confidence_threshold
                        )

                        # Tambahkan deteksi ke sequence
                        for detection in detections:
                            st.session_state.word_sequence.add_detection(
                                detection['word'],
                                detection['confidence']
                            )

                        # Gunakan frame yang sudah diannotasi
                        display_frame = annotated_frame
                    else:
                        display_frame = frame

                    # Convert BGR ke RGB untuk Streamlit
                    frame_rgb = cv2.cvtColor(display_frame, cv2.COLOR_BGR2RGB)

                    # Tampilkan frame
                    video_placeholder.image(frame_rgb, channels="RGB", use_container_width=True)

                    # Hitung dan tampilkan FPS
                    frame_count += 1
                    if frame_count % 30 == 0:  # Update FPS setiap 30 frame
                        elapsed_time = time.time() - start_time
                        fps = frame_count / elapsed_time
                        fps_placeholder.metric("FPS", f"{fps:.1f}")

                # Small delay untuk mencegah overload
                time.sleep(0.01)
        else:
            video_placeholder.info("📹 Klik 'Start' untuk memulai kamera")
    
    with col2:
        st.subheader("🔍 Hasil Deteksi")

        if not st.session_state.camera_running:
            st.info("Kamera belum aktif")
        elif not st.session_state.model_loaded:
            st.warning("Model belum dimuat")
        elif not st.session_state.detection_enabled:
            st.info("Deteksi belum diaktifkan")
        else:
            # Tampilkan sequence kata
            sequence_text = st.session_state.word_sequence.get_sequence_text()

            if sequence_text:
                st.success("**Kalimat Terdeteksi:**")
                st.markdown(f"### 💬 {sequence_text}")

                # Tampilkan detail sequence
                st.subheader("📝 Detail Deteksi")
                for i, item in enumerate(st.session_state.word_sequence.sequence):
                    st.write(f"{i+1}. **{item['word']}** (confidence: {item['confidence']:.2f})")
            else:
                st.info("Belum ada kata yang terdeteksi")
                st.write("Tunjukkan bahasa isyarat di depan kamera untuk mulai mendeteksi.")

if __name__ == "__main__":
    main()
