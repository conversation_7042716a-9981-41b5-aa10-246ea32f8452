# Core dependencies
streamlit>=1.28.0
opencv-python>=4.8.0
numpy>=1.24.0
pillow>=10.0.0

# YOLOv11 and AI/ML dependencies
ultralytics>=8.0.0
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0

# GPU acceleration (CUDA support)
# Install with: pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# Additional utilities
pandas>=2.0.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Performance optimization
threading-timer>=1.0.0
