# Core dependencies
streamlit>=1.25.0
opencv-python>=4.5.0
numpy>=1.21.0
pillow>=8.0.0

# YOLOv11 and AI/ML dependencies
ultralytics>=8.0.0

# PyTorch (install separately for GPU support)
# For GPU: pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
# For CPU: pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
torch>=1.13.0
torchvision>=0.14.0

# Additional utilities (optional)
pandas>=1.5.0
matplotlib>=3.5.0
