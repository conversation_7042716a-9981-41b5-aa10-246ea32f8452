"""
Script untuk instalasi dependencies SIBI Sign Language Detection
"""
import subprocess
import sys
import os
import platform

def run_command(command, description):
    """Menjalankan command dengan error handling"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} berhasil")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} gagal: {e}")
        print(f"Error output: {e.stderr}")
        return False

def check_python_version():
    """Cek versi Python"""
    version = sys.version_info
    print(f"🐍 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ diperlukan")
        return False
    
    print("✅ Python version compatible")
    return True

def detect_gpu():
    """Deteksi GPU dan CUDA"""
    print("🔍 Detecting GPU...")
    
    try:
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name()
            cuda_version = torch.version.cuda
            print(f"✅ GPU detected: {gpu_name}")
            print(f"📊 CUDA version: {cuda_version}")
            return True
        else:
            print("⚠️ No GPU detected, will use CPU")
            return False
    except ImportError:
        print("⚠️ PyTorch not installed yet, will detect GPU after installation")
        return None

def install_basic_requirements():
    """Install requirements.txt"""
    if not os.path.exists("requirements.txt"):
        print("❌ requirements.txt tidak ditemukan")
        return False
    
    return run_command(
        f"{sys.executable} -m pip install -r requirements.txt",
        "Installing basic requirements"
    )

def install_pytorch_gpu():
    """Install PyTorch dengan CUDA support"""
    system = platform.system().lower()
    
    if system == "windows":
        cuda_command = f"{sys.executable} -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118"
    elif system == "linux":
        cuda_command = f"{sys.executable} -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118"
    else:  # macOS
        print("⚠️ CUDA tidak tersedia di macOS, menggunakan CPU version")
        cuda_command = f"{sys.executable} -m pip install torch torchvision torchaudio"
    
    return run_command(cuda_command, "Installing PyTorch with CUDA support")

def install_pytorch_cpu():
    """Install PyTorch CPU-only"""
    cpu_command = f"{sys.executable} -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu"
    return run_command(cpu_command, "Installing PyTorch (CPU-only)")

def verify_installation():
    """Verifikasi instalasi"""
    print("🔍 Verifying installation...")
    
    try:
        import streamlit
        import cv2
        import torch
        import ultralytics
        
        print("✅ All packages imported successfully")
        
        # Test GPU
        if torch.cuda.is_available():
            print(f"✅ GPU available: {torch.cuda.get_device_name()}")
        else:
            print("⚠️ Using CPU")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def create_directories():
    """Buat direktori yang diperlukan"""
    directories = [".streamlit", "models"]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"📁 Created directory: {directory}")
        else:
            print(f"📁 Directory exists: {directory}")

def main():
    """Main installation function"""
    print("🚀 SIBI Sign Language Detection - Dependency Installer")
    print("=" * 60)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Create directories
    create_directories()
    
    # Upgrade pip
    run_command(f"{sys.executable} -m pip install --upgrade pip", "Upgrading pip")
    
    # Install basic requirements
    if not install_basic_requirements():
        print("❌ Failed to install basic requirements")
        return False
    
    # Detect GPU and install appropriate PyTorch
    gpu_available = detect_gpu()
    
    if gpu_available is None:  # PyTorch not installed yet
        # Ask user preference
        print("\n🤔 Choose PyTorch installation:")
        print("1. GPU version (CUDA) - Recommended for better performance")
        print("2. CPU version - For systems without CUDA")
        
        choice = input("Enter choice (1 or 2): ").strip()
        
        if choice == "1":
            if not install_pytorch_gpu():
                print("❌ GPU installation failed, trying CPU version...")
                install_pytorch_cpu()
        else:
            install_pytorch_cpu()
    
    # Verify installation
    if verify_installation():
        print("\n🎉 Installation completed successfully!")
        print("\n📋 Next steps:")
        print("1. Place your model file (sibiv3.pt) in the models/ directory")
        print("2. Run: python test_app.py (to test the system)")
        print("3. Run: python run_app.py (to start the application)")
        return True
    else:
        print("\n❌ Installation verification failed")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n💡 If you encounter issues:")
        print("- Make sure you have Python 3.8+")
        print("- Try running as administrator/sudo")
        print("- Check your internet connection")
        print("- For GPU issues, ensure CUDA drivers are installed")
        sys.exit(1)
