"""
Script untuk testing aplikasi SIBI Sign Language Detection
"""
import cv2
import torch
import os
import time
from ultralytics import YOL<PERSON>

def test_camera():
    """Test kamera functionality"""
    print("🔍 Testing camera...")
    
    cap = cv2.VideoCapture(0)
    if not cap.isOpened():
        print("❌ Kamera tidak dapat diakses")
        return False
    
    # Test capture beberapa frame
    for i in range(5):
        ret, frame = cap.read()
        if not ret:
            print(f"❌ Gagal capture frame {i+1}")
            cap.release()
            return False
        print(f"✅ Frame {i+1} berhasil di-capture")
        time.sleep(0.1)
    
    cap.release()
    print("✅ Kamera berfungsi normal")
    return True

def test_model():
    """Test model loading dan inference"""
    print("🔍 Testing model...")
    
    model_path = "models/sibiv3.pt"
    if not os.path.exists(model_path):
        print(f"❌ Model tidak ditemukan: {model_path}")
        return False
    
    try:
        # Load model
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
        print(f"📱 Using device: {device}")
        
        model = YOLO(model_path)
        model.to(device)
        print("✅ Model berhasil dimuat")
        
        # Test inference dengan dummy image
        import numpy as np
        dummy_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        
        start_time = time.time()
        results = model(dummy_frame, conf=0.5, verbose=False)
        inference_time = time.time() - start_time
        
        print(f"✅ Inference berhasil dalam {inference_time:.3f} detik")
        return True
        
    except Exception as e:
        print(f"❌ Error testing model: {e}")
        return False

def test_gpu():
    """Test GPU availability"""
    print("🔍 Testing GPU...")
    
    if torch.cuda.is_available():
        gpu_name = torch.cuda.get_device_name()
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"✅ GPU tersedia: {gpu_name}")
        print(f"📊 GPU Memory: {gpu_memory:.1f} GB")
        
        # Test CUDA operations
        try:
            x = torch.randn(100, 100).cuda()
            y = torch.randn(100, 100).cuda()
            z = torch.mm(x, y)
            print("✅ CUDA operations berfungsi")
            return True
        except Exception as e:
            print(f"❌ CUDA error: {e}")
            return False
    else:
        print("⚠️ GPU tidak tersedia, akan menggunakan CPU")
        return True

def test_dependencies():
    """Test semua dependencies"""
    print("🔍 Testing dependencies...")
    
    dependencies = [
        'streamlit',
        'cv2',
        'torch',
        'ultralytics'
    ]
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep} tersedia")
        except ImportError:
            print(f"❌ {dep} tidak tersedia")
            return False
    
    return True

def run_performance_test():
    """Test performance aplikasi"""
    print("🔍 Testing performance...")
    
    if not test_model():
        return False
    
    # Load model untuk performance test
    model_path = "models/sibiv3.pt"
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    model = YOLO(model_path)
    model.to(device)
    
    # Test dengan berbagai ukuran frame
    import numpy as np
    
    frame_sizes = [(320, 240), (640, 480), (1280, 720)]
    
    for width, height in frame_sizes:
        dummy_frame = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
        
        # Warm up
        for _ in range(3):
            model(dummy_frame, conf=0.5, verbose=False)
        
        # Measure performance
        times = []
        for _ in range(10):
            start_time = time.time()
            results = model(dummy_frame, conf=0.5, verbose=False)
            times.append(time.time() - start_time)
        
        avg_time = sum(times) / len(times)
        fps = 1.0 / avg_time
        
        print(f"📊 {width}x{height}: {avg_time:.3f}s avg, {fps:.1f} FPS")
    
    return True

def main():
    """Main testing function"""
    print("🚀 SIBI Sign Language Detection - System Test")
    print("=" * 50)
    
    tests = [
        ("Dependencies", test_dependencies),
        ("GPU", test_gpu),
        ("Camera", test_camera),
        ("Model", test_model),
        ("Performance", run_performance_test)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ {test_name} test failed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    all_passed = True
    for test_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{test_name:15} : {status}")
        if not passed:
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 Semua test berhasil! Aplikasi siap digunakan.")
    else:
        print("⚠️ Beberapa test gagal. Periksa konfigurasi sistem.")
    
    return all_passed

if __name__ == "__main__":
    main()
