# 🤟 SIBI Sign Language Detection

Aplikasi deteksi bahasa isyarat Indonesia (SIBI) real-time menggunakan YOLOv11 dan Streamlit.

## 📋 Fitur

- **Deteksi Real-time**: Mendeteksi bahasa isyarat melalui kamera secara real-time
- **9 Kata SIBI**: Dapat mendeteksi kata: mau, saya, mana, makan, kamu, jalan, hotel, ke, di
- **GPU Acceleration**: Mendukung GPU untuk performa optimal
- **Sequence Building**: Menyusun kata-kata yang terdeteksi menjadi kalimat
- **User-friendly Interface**: Interface yang mudah digunakan dengan Streamlit

## 🛠️ Instalasi

### 1. Clone Repository
```bash
git clone <repository-url>
cd sibiweb
```

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Install PyTorch dengan CUDA (untuk GPU)
```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### 4. Pastikan Model Ada
Pastikan file model `sibiv3.pt` ada di folder `models/`

## 🚀 Cara Menjalankan

### Opsi 1: Menggunakan Script Runner
```bash
python run_app.py
```

### Opsi 2: Langsung dengan Streamlit
```bash
streamlit run app.py
```

## 📱 Cara Penggunaan

1. **Start Kamera**: Klik tombol "▶️ Start" di sidebar
2. **Load Model**: Klik tombol "📥 Load Model" untuk memuat model YOLOv11
3. **Aktifkan Deteksi**: Centang "Aktifkan Deteksi Real-time"
4. **Mulai Deteksi**: Tunjukkan bahasa isyarat di depan kamera
5. **Lihat Hasil**: Kata-kata yang terdeteksi akan muncul di panel kanan

## ⚙️ Pengaturan

- **Confidence Threshold**: Atur tingkat kepercayaan deteksi (0.1 - 1.0)
- **Pilih Kamera**: Pilih kamera yang akan digunakan (0, 1, 2)
- **Clear Sequence**: Hapus sequence kata yang sudah terdeteksi

## 🎯 Kata yang Dapat Dideteksi

1. mau
2. saya
3. mana
4. makan
5. kamu
6. jalan
7. hotel
8. ke
9. di

## 🔧 Optimasi Performance

### Untuk Mengurangi Lag:
- Aplikasi menggunakan threading untuk capture frame
- Buffer minimal untuk mengurangi delay
- Frame skipping untuk optimasi
- GPU acceleration jika tersedia

### Pengaturan Kamera:
- Resolusi: 640x480 (optimal untuk deteksi)
- FPS Target: 30
- Buffer Size: 1 (minimal lag)

## 📊 Monitoring

- **FPS Counter**: Menampilkan frame rate real-time
- **GPU Status**: Menampilkan status penggunaan GPU
- **Detection Status**: Status model dan deteksi
- **Confidence Score**: Skor kepercayaan untuk setiap deteksi

## 🐛 Troubleshooting

### Kamera Tidak Berfungsi
- Pastikan kamera tidak digunakan aplikasi lain
- Coba ganti index kamera (0, 1, 2)
- Restart aplikasi

### Model Tidak Ditemukan
- Pastikan file `sibiv3.pt` ada di folder `models/`
- Cek path model di kode

### Performance Lambat
- Pastikan GPU drivers terinstall
- Kurangi resolusi kamera jika perlu
- Tutup aplikasi lain yang menggunakan GPU

### GPU Tidak Terdeteksi
- Install CUDA toolkit
- Install PyTorch dengan CUDA support
- Restart sistem setelah install driver

## 📁 Struktur Project

```
sibiweb/
├── app.py              # Aplikasi utama Streamlit
├── run_app.py          # Script untuk menjalankan aplikasi
├── requirements.txt    # Dependencies
├── README.md          # Dokumentasi
└── models/
    └── sibiv3.pt      # Model YOLOv11
```

## 🔄 Update dan Maintenance

- Model dapat diupdate dengan mengganti file `sibiv3.pt`
- Dependencies dapat diupdate melalui `requirements.txt`
- Konfigurasi dapat diubah di `app.py`

## 📞 Support

Jika mengalami masalah, pastikan:
1. Semua dependencies terinstall
2. Model file ada dan dapat diakses
3. Kamera berfungsi normal
4. GPU drivers up-to-date (jika menggunakan GPU)

---

**Dibuat untuk membantu komunikasi tuli dalam berwisata** 🌟
