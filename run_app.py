"""
Script untuk menjalankan aplikasi SIBI Sign Language Detection
"""
import subprocess
import sys
import os

def check_dependencies():
    """Cek apakah dependencies sudah terinstall"""
    try:
        import streamlit
        import cv2
        import torch
        import ultralytics
        print("✅ Semua dependencies sudah terinstall")
        return True
    except ImportError as e:
        print(f"❌ Missing dependency: {e}")
        print("Jalankan: pip install -r requirements.txt")
        return False

def check_model():
    """Cek apakah model file ada"""
    model_path = "models/sibiv3.pt"
    if os.path.exists(model_path):
        print(f"✅ Model ditemukan: {model_path}")
        return True
    else:
        print(f"❌ Model tidak ditemukan: {model_path}")
        print("Pastikan file model ada di folder models/")
        return False

def check_gpu():
    """Cek status GPU"""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name()
            print(f"✅ GPU tersedia: {gpu_name}")
        else:
            print("⚠️ GPU tidak tersedia, akan menggunakan CPU")
    except:
        print("⚠️ Tidak dapat mengecek status GPU")

def run_app():
    """Menjalankan aplikasi Streamlit"""
    print("🚀 Memulai aplikasi SIBI Sign Language Detection...")
    
    # Cek semua requirements
    if not check_dependencies():
        return
    
    if not check_model():
        return
    
    check_gpu()
    
    # Jalankan streamlit
    try:
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.address", "localhost",
            "--server.port", "8501",
            "--browser.gatherUsageStats", "false"
        ])
    except KeyboardInterrupt:
        print("\n👋 Aplikasi dihentikan")
    except Exception as e:
        print(f"❌ Error menjalankan aplikasi: {e}")

if __name__ == "__main__":
    run_app()
